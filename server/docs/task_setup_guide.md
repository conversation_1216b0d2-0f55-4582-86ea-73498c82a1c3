# 任务系统设置指南

## 快速开始

### 1. 重启应用
重启应用以创建新的数据表：
```bash
cd server
go run main.go
```

### 2. 导入示例数据
执行以下SQL语句导入任务数据：
```sql
-- 插入任务数据
INSERT INTO `sys_tasks` (`title`, `description`, `reward`, `task_type`, `is_enabled`, `is_repeatable`, `sort`, `icon`, `remark`, `created_at`, `updated_at`) VALUES
('今日已获赠30体力', '每日签到获得体力奖励（不累加）', 30, 'daily', 1, 1, 1, '🎁', '每日签到任务', NOW(), NOW()),
('邀请朋友安装体验+100体力', '邀请新用户注册并安装应用', 100, 'once', 1, 1, 2, '👥', '邀请好友任务', NOW(), NOW()),
('分享AI执行任务的案例并入选案例库+50体力', '分享优质AI执行任务案例', 50, 'once', 1, 1, 3, '📝', '案例分享任务', NOW(), NOW()),
('反馈意见或发布您的需求，被标星+50体力', '提供有价值的反馈或需求建议', 50, 'once', 1, 1, 4, '⭐', '反馈建议任务', NOW(), NOW()),
('会员每月+3000体力', '会员专享每月体力奖励', 3000, 'monthly', 1, 1, 5, '👑', '会员月度奖励', NOW(), NOW());

-- 插入API权限
INSERT INTO `sys_apis` (`path`, `description`, `api_group`, `method`, `created_at`, `updated_at`) VALUES
('/task/getTaskList', '获取任务列表', '任务管理', 'POST', NOW(), NOW()),
('/task/completeTask', '完成任务', '任务管理', 'POST', NOW(), NOW()),
('/task/getTaskById', '根据ID获取任务详情', '任务管理', 'GET', NOW(), NOW());
```

### 3. 配置权限（可选）
如果需要为特定角色配置任务权限，可以在系统管理中进行配置。

### 4. 测试接口
使用以下curl命令测试接口（需要替换JWT_TOKEN）：

```bash
# 获取任务列表
curl -X POST "http://localhost:8888/task/getTaskList" \
  -H "Content-Type: application/json" \
  -H "x-token: YOUR_JWT_TOKEN" \
  -d '{}'

# 完成任务
curl -X POST "http://localhost:8888/task/completeTask" \
  -H "Content-Type: application/json" \
  -H "x-token: YOUR_JWT_TOKEN" \
  -d '{
    "task_id": 1
  }'

# 获取任务详情
curl -X GET "http://localhost:8888/task/getTaskById?id=1" \
  -H "x-token: YOUR_JWT_TOKEN"
```

## 自定义任务

### 添加新任务
直接在数据库中插入新任务：
```sql
INSERT INTO `sys_tasks` (`title`, `description`, `reward`, `task_type`, `is_enabled`, `is_repeatable`, `sort`, `icon`, `remark`, `created_at`, `updated_at`) VALUES
('您的任务标题', '任务描述', 积分数量, '任务类型', 1, 1, 排序, '图标', '备注', NOW(), NOW());
```

### 任务类型说明
- `daily`: 每日任务，每天可完成一次
- `weekly`: 每周任务，每周可完成一次
- `monthly`: 每月任务，每月可完成一次
- `once`: 一次性任务，根据`is_repeatable`决定是否可重复
- `daily_repeatable`: 每日可重复任务，每天可多次完成

### 业务逻辑集成
在您的业务代码中调用完成任务接口：
```go
// 示例：用户完成某个业务操作后自动完成任务
func (s *YourService) SomeBusinessAction(userID uint) error {
    // 执行业务逻辑
    // ...
    
    // 完成相关任务
    taskService := system.TaskServiceApp
    _, err := taskService.CompleteTask(userID, taskID)
    if err != nil {
        // 记录日志，但不影响主业务流程
        global.GVA_LOG.Warn("完成任务失败", zap.Error(err))
    }
    
    return nil
}
```

## 注意事项

1. **数据一致性**: 任务完成使用了数据库事务，确保积分更新和记录的一致性
2. **重复检查**: 系统会自动检查任务是否可以重复完成
3. **错误处理**: 任务完成失败不会影响主业务流程
4. **性能考虑**: 任务列表接口支持分页，避免一次性加载过多数据
5. **扩展性**: 可以通过修改`task_type`和相关逻辑来支持更多任务类型

## 故障排除

### 常见问题
1. **表不存在**: 确保重启了应用，让数据库迁移生效
2. **权限不足**: 检查API权限配置和用户角色权限
3. **任务无法完成**: 检查任务状态和完成条件
4. **积分未更新**: 检查数据库事务是否正常执行

### 日志查看
任务相关的日志会记录在应用日志中，可以通过以下方式查看：
```bash
# 查看应用日志
tail -f logs/server.log | grep -i task
```
