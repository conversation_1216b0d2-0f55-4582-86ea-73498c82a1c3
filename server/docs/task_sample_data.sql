-- 任务列表示例数据
-- 根据您提供的任务列表创建的示例数据

-- 插入任务数据
INSERT INTO `sys_tasks` (`title`, `description`, `reward`, `task_type`, `is_enabled`, `is_repeatable`, `sort`, `icon`, `remark`, `created_at`, `updated_at`) VALUES
('今日已获赠30体力', '每日签到获得体力奖励（不累加）', 30, 'daily', 1, 1, 1, '🎁', '每日签到任务', NOW(), NOW()),
('邀请朋友安装体验+100体力', '邀请新用户注册并安装应用', 100, 'once', 1, 1, 2, '👥', '邀请好友任务', NOW(), NOW()),
('分享AI执行任务的案例并入选案例库+50体力', '分享优质AI执行任务案例', 50, 'once', 1, 1, 3, '📝', '案例分享任务', NOW(), NOW()),
('反馈意见或发布您的需求，被标星+50体力', '提供有价值的反馈或需求建议', 50, 'once', 1, 1, 4, '⭐', '反馈建议任务', NOW(), NOW()),
('会员每月+3000体力', '会员专享每月体力奖励', 3000, 'monthly', 1, 1, 5, '👑', '会员月度奖励', NOW(), NOW()),
('观看广告+5体力', '观看广告获得体力，每日可多次完成', 5, 'daily_repeatable', 1, 1, 6, '📺', '观看广告任务', NOW(), NOW()),
('完成问卷调查+100体力', '参与月度问卷调查', 100, 'monthly', 1, 0, 7, '📋', '月度问卷任务', NOW(), NOW()),
('系统每日免费积分刷新', '系统自动刷新每日免费积分到50', 0, 'daily', 0, 1, 100, '🔄', '系统任务', NOW(), NOW()),
('会员购买奖励', '购买会员获得积分奖励', 0, 'once', 0, 1, 101, '💎', '系统任务', NOW(), NOW()),
('系统初始化免费积分', '系统为新用户初始化免费积分', 0, 'once', 0, 1, 102, '🎯', '系统任务', NOW(), NOW());

-- 注意：
-- 1. task_type 字段说明：
--    - 'daily': 日常任务，每天可完成一次
--    - 'once': 一次性任务，只能完成一次
--    - 'weekly': 每周任务，每周可完成一次
--    - 'monthly': 每月任务，每月可完成一次
--    - 'daily_repeatable': 每日可重复任务，每天可多次完成
--
-- 2. is_repeatable 字段说明：
--    - 1: 可重复完成（配合task_type使用）
--    - 0: 不可重复完成
--
-- 3. 用户完成任务后，需要在 sys_user_tasks 表中记录完成情况
-- 4. 同时在 sys_user_points 表中记录积分变动，type字段设置为'task'，task_id关联任务ID

-- 示例：用户完成任务的记录（假设用户ID为1，任务ID为1）
-- INSERT INTO `sys_user_tasks` (`user_id`, `task_id`, `completed_at`, `reward`, `status`, `created_at`, `updated_at`) VALUES
-- (1, 1, NOW(), 30, 'completed', NOW(), NOW());

-- 示例：积分流水记录（假设用户ID为1，任务ID为1，用户任务记录ID为1）
-- INSERT INTO `sys_user_points` (`user_id`, `change`, `reason`, `type`, `task_id`, `user_task_id`, `created_at`, `updated_at`) VALUES
-- (1, 30, '完成任务：今日已获赠30体力', 'task', 1, 1, NOW(), NOW());

-- 任务相关API权限配置
INSERT INTO `sys_apis` (`path`, `description`, `api_group`, `method`, `created_at`, `updated_at`) VALUES
('/task/getTaskList', '获取任务列表', '任务管理', 'POST', NOW(), NOW()),
('/task/completeTask', '完成任务', '任务管理', 'POST', NOW(), NOW()),
('/task/getTaskById', '根据ID获取任务详情', '任务管理', 'GET', NOW(), NOW());

-- 系统任务ID说明（用于代码中的常量配置）：
-- ID=5: 会员每月+3000体力 (MEMBERSHIP_MONTHLY_TASK_ID)
-- ID=8: 系统每日免费积分刷新 (DAILY_FREE_POINTS_TASK_ID)
-- ID=9: 会员购买奖励 (MEMBERSHIP_PURCHASE_TASK_ID)
-- ID=10: 系统初始化免费积分 (INIT_FREE_POINTS_TASK_ID)
