# 任务系统API测试文档

## 接口列表

### 1. 获取任务列表
**接口地址**: `POST /task/getTaskList`

**请求参数**:
```json
{
  "task_type": "daily",  // 可选：daily, once, weekly, monthly, daily_repeatable
  "is_enabled": true     // 可选：true/false
}
```

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "ID": 1,
        "CreatedAt": "2024-01-01T00:00:00Z",
        "UpdatedAt": "2024-01-01T00:00:00Z",
        "title": "今日已获赠30体力",
        "description": "每日签到获得体力奖励（不累加）",
        "reward": 30,
        "task_type": "daily",
        "is_enabled": true,
        "is_repeatable": true,
        "sort": 1,
        "icon": "🎁",
        "remark": "每日签到任务",
        "is_completed_today": true,
        "total_completed_times": 5,
        "total_reward_points": 150,
        "last_completed_at": "2024-01-01T08:00:00Z"
      }
    ],
    "total": 5,
    "page": 1,
    "pageSize": 10
  },
  "msg": "获取任务列表成功"
}
```

### 2. 完成任务
**接口地址**: `POST /task/completeTask`

**请求参数**:
```json
{
  "task_id": 1
}
```

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "task_id": 1,
    "task_title": "今日已获赠30体力",
    "reward_points": 30,
    "message": "恭喜您完成任务「今日已获赠30体力」，获得 30 体力！"
  },
  "msg": "完成任务成功"
}
```

**错误响应示例**:
```json
{
  "code": 7,
  "data": {},
  "msg": "今日已完成该任务"
}
```

### 3. 根据ID获取任务详情
**接口地址**: `GET /task/getTaskById?id=1`

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "ID": 1,
    "CreatedAt": "2024-01-01T00:00:00Z",
    "UpdatedAt": "2024-01-01T00:00:00Z",
    "title": "今日已获赠30体力",
    "description": "每日签到获得体力奖励（不累加）",
    "reward": 30,
    "task_type": "daily",
    "is_enabled": true,
    "is_repeatable": true,
    "sort": 1,
    "icon": "🎁",
    "remark": "每日签到任务"
  },
  "msg": "获取任务详情成功"
}
```

## 测试步骤

1. **启动服务器**
   ```bash
   cd server
   go run main.go
   ```

2. **导入示例数据**
   ```sql
   -- 执行 task_sample_data.sql 中的SQL语句
   ```

3. **获取JWT Token**
   - 先调用登录接口获取token
   - 在后续请求的Header中添加：`x-token: your_jwt_token`

4. **测试接口**
   - 使用Postman或curl测试上述接口
   - 注意需要在Header中添加认证token

## 注意事项

1. 所有接口都需要JWT认证
2. 任务完成状态会根据任务类型进行检查：
   - daily: 每日只能完成一次
   - weekly: 每周只能完成一次
   - monthly: 每月只能完成一次
   - once: 根据is_repeatable字段决定是否可重复
   - daily_repeatable: 每日可重复完成，无次数限制
3. 完成任务后会自动更新用户积分并记录流水
