package main

import (
	"flag"
	"fmt"
	"os"

	"github.com/spf13/viper"

	"github.com/flipped-aurora/gin-vue-admin/server/cmd"
	"github.com/flipped-aurora/gin-vue-admin/server/core"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/initialize"
	_ "go.uber.org/automaxprocs"
	"go.uber.org/zap"
)

//go:generate go env -w GO111MODULE=on
//go:generate go env -w GOPROXY=https://goproxy.cn,direct
//go:generate go mod tidy
//go:generate go mod download

// 这部分 @Tag 设置用于排序, 需要排序的接口请按照下面的格式添加
// swag init 对 @Tag 只会从入口文件解析, 默认 main.go
// 也可通过 --generalInfo flag 指定其他文件
// @Tag.Name        Base
// @Tag.Name        SysUser
// @Tag.Description 用户

// @title                       Gin-Vue-Admin Swagger API接口文档
// @version                     v2.8.0
// @description                 使用gin+vue进行极速开发的全栈开发基础平台
// @securityDefinitions.apikey  ApiKeyAuth
// @in                          header
// @name                        x-token
// @BasePath                    /
func main() {
	// 如果是 proxy 子命令，只执行 cobra，不启动主服务
	if len(os.Args) > 1 && os.Args[1] == "proxy" {
		cmd.Execute()
		return
	}

	// 解析命令行参数
	var env string
	flag.StringVar(&env, "env", "", "specify environment (dev/prod)")
	flag.Parse()

	// 否则启动 Gin 主服务
	global.GVA_VP = initViperWithEnv(env) // 根据环境初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)

	global.GVA_DB = initialize.Gorm() // gorm连接数据库

	// 初始化Redis（如果配置了的话）
	if global.GVA_CONFIG.System.UseMultipoint || global.GVA_CONFIG.System.UseRedis {
		initialize.Redis()
		initialize.RedisList()
	}

	// 在Redis初始化后初始化事件系统
	initialize.InitEvents()

	// 初始化WebSocket服务
	initialize.InitWebSocketService()
	initialize.Timer()
	initialize.DBList()
	if global.GVA_DB != nil {
		initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
	
	// 初始化 PanSou 搜索服务
	if err := initialize.InitPanSou(); err != nil {
		global.GVA_LOG.Error("初始化 PanSou 失败", zap.Error(err))
	}
	
	core.RunWindowsServer()
}

// initViperWithEnv 根据环境参数初始化 Viper
func initViperWithEnv(env string) *viper.Viper {
	var configFile string

	// 根据环境参数选择配置文件
	switch env {
	case "dev":
		configFile = "config.dev.yaml"
		fmt.Printf("使用开发环境配置: %s\n", configFile)
	case "prod":
		configFile = "config.prod.yaml"
		fmt.Printf("使用生产环境配置: %s\n", configFile)
	default:
		// 如果没有指定环境，使用默认配置文件（本地环境）
		fmt.Println("未指定环境，使用默认配置（本地环境）: config.yaml")
		return core.Viper()
	}

	// 使用指定的配置文件初始化
	return core.Viper(configFile)
}
