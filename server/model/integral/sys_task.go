package integral

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// SysTask 系统任务表
// 用于存储系统任务信息，用户完成任务可获得体力（积分）
type SysTask struct {
	global.GVA_MODEL
	Title        string `json:"title" gorm:"not null;comment:任务标题"`                           // 任务标题
	Description  string `json:"description" gorm:"type:text;comment:任务描述"`                    // 任务描述
	Reward       int    `json:"reward" gorm:"not null;default:0;comment:奖励体力值"`               // 奖励体力值
	TaskType     string `json:"task_type" gorm:"type:varchar(20);default:daily;comment:任务类型"` // 任务类型：daily(日常), once(一次性), weekly(每周)
	IsEnabled    bool   `json:"is_enabled" gorm:"default:true;comment:是否启用"`                  // 是否启用
	IsRepeatable bool   `json:"is_repeatable" gorm:"default:true;comment:是否可重复完成"`            // 是否可重复完成
	Sort         int    `json:"sort" gorm:"default:0;comment:排序"`                             // 排序
	Icon         string `json:"icon" gorm:"comment:任务图标"`                                     // 任务图标
	Remark       string `json:"remark" gorm:"comment:备注"`                                     // 备注
	Value        string `json:"value" gorm:"comment:任务值"`                                     // 任务值
}

func (SysTask) TableName() string {
	return "sys_tasks"
}

// 任务类型常量
const (
	TaskTypeDaily           = "daily"            // 日常任务
	TaskTypeOnce            = "once"             // 一次性任务
	TaskTypeWeekly          = "weekly"           // 每周任务
	TaskTypeMonthly         = "monthly"          // 每月任务
	TaskTypeDailyRepeatable = "daily_repeatable" // 每日可重复任务
)
