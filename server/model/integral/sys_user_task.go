package integral

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

// SysUserTask 用户任务完成记录表
// 记录用户完成任务的情况
type SysUserTask struct {
	global.GVA_MODEL
	UserID uint   `json:"user_id" gorm:"index;not null;comment:用户ID"`                  // 用户ID
	TaskID uint   `json:"task_id" gorm:"index;not null;comment:任务ID"`                  // 任务ID
	Reward int    `json:"reward" gorm:"not null;comment:获得的体力值"`                       // 获得的体力值
	Status string `json:"status" gorm:"type:varchar(20);default:completed;comment:状态"` // 状态：completed(已完成), cancelled(已取消)
	Remark string `json:"remark" gorm:"comment:备注"`                                    // 备注
	RefId  uint   `json:"ref_id" gorm:"comment:关联ID"`                                  // 关联ID,反馈id，分享AI执行任务案例id，被邀请人id

	// 关联字段
	User system.SysUser `json:"user" gorm:"foreignKey:UserID;references:ID"` // 关联用户
	Task SysTask        `json:"task" gorm:"foreignKey:TaskID;references:ID"` // 关联任务
}

func (SysUserTask) TableName() string {
	return "sys_user_tasks"
}

// 用户任务状态常量
const (
	UserTaskStatusCompleted = "completed" // 已完成
	UserTaskStatusCancelled = "cancelled" // 已取消
)
