package response

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
)

// TaskListItem 任务列表项
type TaskListItem struct {
	integral.SysTask
	TotalCompletedTimes int              `json:"total_completed_times"` // 累计完成次数
	TotalRewardPoints   int              `json:"total_reward_points"`   // 累计获得积分
	LastCompletedAt     *global.DateTime `json:"last_completed_at"`     // 最后完成时间
}

// TaskListResponse 任务列表响应
type TaskListResponse struct {
	List []TaskListItem `json:"list"`
}

// CompleteTaskResponse 完成任务响应
type CompleteTaskResponse struct {
	TaskID       uint   `json:"task_id"`       // 任务ID
	TaskTitle    string `json:"task_title"`    // 任务标题
	RewardPoints int    `json:"reward_points"` // 获得的积分
	Message      string `json:"message"`       // 提示信息
}
