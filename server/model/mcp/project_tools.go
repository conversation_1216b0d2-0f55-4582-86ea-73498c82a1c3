// 自动生成模板ProjectTools
package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
)

// projectTools表 结构体  ProjectTools
type ProjectTools struct {
	global.GVA_MODEL
	ProjectUUId         *string        `json:"projectUUId" form:"projectUUId" gorm:"column:project_uuid;:varchar(191);"`                                                           //projectUUId字段
	ProjectId           *int           `json:"projectId" form:"projectId" gorm:"column:project_id;comment:;size:20;"`                                                              //projectId字段
	Name                *string        `json:"name" form:"name" gorm:"column:name;comment:;size:255;"`                                                                             //name字段
	Description         *string        `json:"description" form:"description" gorm:"column:description;comment:;type:text;"`                                                       //description字段
	DescriptionChinese  *string        `json:"descriptionChinese" form:"descriptionChinese" gorm:"column:description_chinese;comment:;type:text;"`                                 //descriptionChinese字段
	InputSchema         datatypes.JSON `json:"inputSchema" form:"inputSchema" gorm:"column:input_schema;comment:;type:text;" swaggertype:"object"`                                 //inputSchema字段
	Points              *int           `json:"points" form:"points" gorm:"column:points;comment:;size:10;"`                                                                        //points字段
	OutputSchema        datatypes.JSON `json:"outputSchema" form:"outputSchema" gorm:"column:output_schema;comment:;type:text;" swaggertype:"object"`                              //outputSchema字段
	Regex               *string        `json:"regex" form:"regex" gorm:"column:regex;type:text;comment:正则表达式"`                                                                     // 匹配内容的正则表达式
	CName               *string        `json:"c_name" form:"c_name" gorm:"column:c_name;type:varchar(255);comment:工具中文名"`                                                          // 工具中文名
	IsSingleCall        *int           `json:"is_single_call" form:"is_single_call" gorm:"column:is_single_call;type:tinyint(1);default:1;comment:是否可以单独调用"`                       // 是否可以单独调用,默认1
	SupportedExtensions datatypes.JSON `json:"supportedExtensions" form:"supportedExtensions" gorm:"column:supported_extensions;comment:支持的文件扩展名;type:text;" swaggertype:"object"` // 支持的文件扩展名，JSON数组格式如["jpeg", "jpg", "png", "folder"]
	MultiFileType       *int           `json:"multiFileType" form:"multiFileType" gorm:"column:multi_file_type;comment:多文件处理类型;size:10;default:0"`                                 // 多文件处理类型：0-单文件，1-不同类型多文件，2-批量处理同类文件
	Keywords            *string        `json:"keywords" form:"keywords" gorm:"column:keywords;comment:关键词用于RAG检索;type:text;"`                                                      // 关键词，用于RAG检索辅助
	Platforms           datatypes.JSON `json:"platforms" form:"platforms" gorm:"column:platforms;comment:支持的平台;type:text;" swaggertype:"object"`                                   // 支持的平台，JSON数组格式如["mac", "windows", "linux"]
	CanHandleDirectory  *int           `json:"canHandleDirectory" form:"canHandleDirectory" gorm:"column:can_handle_directory;comment:是否可处理目录;size:10;default:0"`                  // 是否可处理目录：0-否，1-是
	PrerequisiteToolId  *int           `json:"prerequisiteToolId" form:"prerequisiteToolId" gorm:"column:prerequisite_tool_id;comment:前置工具ID;size:20;"`                            // 前置工具ID，调用时必须先调用的工具
	CanDirectExecute    *int           `json:"canDirectExecute" form:"canDirectExecute" gorm:"column:can_direct_execute;comment:是否可直接执行;size:10;default:0"`                        // 是否可直接执行：0-否，1-是（如分屏、调节音量等）
	IsDangerous         *int           `json:"isDangerous" form:"isDangerous" gorm:"column:is_dangerous;comment:是否危险操作;size:10;default:0"`                                         // 是否危险操作：0-否，1-是（如重启、关机等）
	IsDisabled          *int           `json:"isDisabled" form:"isDisabled" gorm:"column:is_disabled;comment:是否被禁用;size:10;default:0"`                                             // 是否被禁用：0-否，1-是（如获取时间等工具）
	IsError             *int           `json:"isError" form:"isError" gorm:"column:is_error;comment:是否质检不通过（isError=0为通过，表示包含错误字段isError）;size:10;default:0"`                      // 是否质检不通过（isError=1为通过，表示包含错误字段isError）：0-否，1-是
	IsRequiredParameter *int           `json:"isRequiredParameter" form:"isRequiredParameter" gorm:"column:is_required_parameter;comment:是否必填参数;size:10;default:0"`                // 是否必填参数：0-否，1-是
	Logo                *string        `json:"logo" form:"logo" gorm:"column:logo;comment:生成mcp图片logo;type:varchar(255);default:null"`                                             // 生成mcp图片logo
	UsageGif            *string        `json:"usageGif" form:"usageGif" gorm:"column:usage_gif;comment:使用案例GIF;type:varchar(255);default:null"`                                    // 使用案例动图地址（GIF）
	UsageGifDuration    *int           `json:"usageGifDuration" form:"usageGifDuration" gorm:"column:usage_gif_duration;comment:GIF时长(毫秒);type:int;default:null"`                  // GIF时长(毫秒)
}

// TableName projectTools表 ProjectTools自定义表名 project_tools
func (ProjectTools) TableName() string {
	return "project_tools"
}
