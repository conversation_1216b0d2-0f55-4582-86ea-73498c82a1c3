package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// ProjectToolsSearch 项目工具搜索条件
type ProjectToolsSearch struct {
	request.PageInfo
	ProjectID *uint   `json:"project_id" form:"project_id"` // 项目ID
	Name      *string `json:"name" form:"name"`             // 工具名称
	Type      *string `json:"type" form:"type"`             // 工具类型
	Status    *string `json:"status" form:"status"`         // 状态
}
