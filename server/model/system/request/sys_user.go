package request

import (
	common "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

// Register User register structure
type Register struct {
	Username     string `json:"userName" example:"用户名"`
	Password     string `json:"passWord" example:"密码"`
	NickName     string `json:"nickName" example:"昵称"`
	HeaderImg    string `json:"headerImg" example:"头像链接"`
	AuthorityId  uint   `json:"authorityId" swaggertype:"string" example:"int 角色id"`
	Enable       int    `json:"enable" swaggertype:"string" example:"int 是否启用"`
	AuthorityIds []uint `json:"authorityIds" swaggertype:"string" example:"[]uint 角色id"`
	Phone        string `json:"phone" example:"电话号码"`
	Email        string `json:"email" example:"电子邮箱"`
}

// Login User login structure
type Login struct {
	Username  string `json:"username"`  // 用户名
	Password  string `json:"password"`  // 密码
	Captcha   string `json:"captcha"`   // 验证码
	CaptchaId string `json:"captchaId"` // 验证码ID
	Validate  string `json:"validate"`  // 易盾验证参数
}

// 实现CaptchaRequest接口
func (l *Login) GetCaptcha() string {
	return l.Captcha
}

func (l *Login) GetCaptchaId() string {
	return l.CaptchaId
}

func (l *Login) GetValidate() string {
	return l.Validate
}

// ChangePasswordReq Modify password structure
type ChangePasswordReq struct {
	ID          uint   `json:"-"`           // 从 JWT 中提取 user id，避免越权
	Password    string `json:"password"`    // 密码
	NewPassword string `json:"newPassword"` // 新密码
	AuthingId   string `json:"authingId"`   // Authing用户ID
}

// SetUserAuth Modify user's auth structure
type SetUserAuth struct {
	AuthorityId uint `json:"authorityId"` // 角色ID
}

// SetUserAuthorities Modify user's auth structure
type SetUserAuthorities struct {
	ID           uint
	AuthorityIds []uint `json:"authorityIds"` // 角色ID
}

type ChangeUserInfo struct {
	ID           uint                  `gorm:"primarykey"`                                                                           // 主键ID
	NickName     string                `json:"nickName" gorm:"default:系统用户;comment:用户昵称"`                                            // 用户昵称
	Phone        string                `json:"phone"  gorm:"comment:用户手机号"`                                                          // 用户手机号
	AuthorityIds []uint                `json:"authorityIds" gorm:"-"`                                                                // 角色ID
	Email        string                `json:"email"  gorm:"comment:用户邮箱"`                                                           // 用户邮箱
	HeaderImg    string                `json:"headerImg" gorm:"default:https://qmplusimg.henrongyi.top/gva_header.jpg;comment:用户头像"` // 用户头像
	SideMode     string                `json:"sideMode"  gorm:"comment:用户侧边主题"`                                                      // 用户侧边主题
	Enable       int                   `json:"enable" gorm:"comment:冻结用户"`                                                           //冻结用户
	Authorities  []system.SysAuthority `json:"-" gorm:"many2many:sys_user_authority;"`
	AuthingId    string                `json:"authingId"` // Authing用户ID
}

type GetUserList struct {
	common.PageInfo
	Username string `json:"username" form:"username"`
	NickName string `json:"nickName" form:"nickName"`
	Phone    string `json:"phone" form:"phone"`
	Email    string `json:"email" form:"email"`
}

// PhoneRegister 手机号注册结构体
type PhoneRegister struct {
	Phone       string `json:"phone" binding:"required" example:"手机号"`
	Code        string `json:"code" binding:"required" example:"验证码"`
	Password    string `json:"password" binding:"required" example:"密码"`
	NickName    string `json:"nickName" example:"昵称"`
	AuthorityId uint   `json:"authorityId" swaggertype:"string" example:"int 角色id"`
	Enable      int    `json:"enable" swaggertype:"string" example:"int 是否启用"`
	Validate    string `json:"validate" example:"易盾验证参数"`
}

// BindPhoneReq Modify password structure
type BindPhoneReq struct {
	Phone string `json:"phone"` // 手机号
	Code  string `json:"code"`  // 验证码
}

// WechatAuthRequest 微信授权登录请求
type WechatAuthRequest struct {
	Code  string `json:"code"`  // 微信授权code
	State string `json:"state"` // 防止CSRF攻击的state参数
}

// WechatBindRequest 微信绑定已有账号请求
type WechatBindRequest struct {
	Code     string `json:"code" binding:"required"`     // 微信授权code
	State    string `json:"state" binding:"required"`    // 防止CSRF攻击的state参数
	Username string `json:"username" binding:"required"` // 要绑定的用户名
	Password string `json:"password" binding:"required"` // 密码
}

// WechatRegisterRequest 微信授权注册新账号请求
type WechatRegisterRequest struct {
	Code     string `json:"code" binding:"required"`     // 微信授权code
	State    string `json:"state" binding:"required"`    // 防止CSRF攻击的state参数
	Username string `json:"username" binding:"required"` // 新用户名
	Password string `json:"password" binding:"required"` // 密码
	NickName string `json:"nickName"`                    // 昵称（可选，默认使用微信昵称）
	Phone    string `json:"phone"`                       // 手机号（可选）
	Email    string `json:"email"`                       // 邮箱（可选）
}

// SendSMSCodeReq 发送短信验证码请求
type SendSMSCodeReq struct {
	Phone    string `json:"phone" binding:"required" example:"手机号"`
	SmsType  int    `json:"smsType" binding:"required" example:"短信类型：1-注册验证码，2-登录验证码"`
	Validate string `json:"validate"` // 易盾验证参数
}

type WechatBindPhoneRequest struct {
	OpenId   string `json:"openId" binding:"required"`
	UnionId  string `json:"unionId" binding:"required"`
	Phone    string `json:"phone" binding:"required"`
	Code     string `json:"code" binding:"required"`
	NickName string `json:"nickName"`
	Avatar   string `json:"avatar"`
}

// ResetPasswordReq 找回密码请求
type ResetPasswordReq struct {
	Phone       string `json:"phone" binding:"required" example:"手机号"`
	Code        string `json:"code" binding:"required" example:"短信验证码"`
	NewPassword string `json:"newPassword" binding:"required" example:"新密码"`
}
