package translate

import (
	"net/http"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"go.uber.org/zap"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/basetypes"
	"github.com/gin-gonic/gin"
)

type TranslateApi struct{}

type TranslateRequest struct {
	Text string `json:"text" binding:"required"`
	From string `json:"from"`
	To   string `json:"to"`
}

type TranslateResponse struct {
	Result string `json:"result"`
}

type LanguageListResponse struct {
	Languages []map[string]string `json:"languages"`
}

// TranslateHandler handles translation requests
func (t *TranslateApi) TranslateHandler(c *gin.Context) {
	var req TranslateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	if req.From == "" {
		req.From = string(basetypes.LangAuto)
	}
	if req.To == "" {
		req.To = string(basetypes.LangEn)
	}
	result, err := translateService.BaiduTranslate(req.Text, basetypes.Language(req.From), basetypes.Language(req.To))
	if err != nil {
		global.GVA_LOG.Error("翻译失败!", zap.Error(err))
		response.FailWithMessage("翻译失败:"+err.Error(), c)
		return
	}
	response.OkWithData(result, c)
}

// ListLanguagesHandler returns all supported languages
func (t *TranslateApi) ListLanguagesHandler(c *gin.Context) {
	langs := translateService.ListLanguages()
	response.OkWithData(langs, c)
}
