package integral

import "github.com/flipped-aurora/gin-vue-admin/server/service"

type ApiGroup struct {
	SysUserPointsApi
	TaskApi
	FeedbackApi
}

var (
	taskService       = service.ServiceGroupApp.IntegralServiceGroup.TaskService
	userPointsService = service.ServiceGroupApp.IntegralServiceGroup.SysUserPointsService
	feedbackService   = service.ServiceGroupApp.IntegralServiceGroup.FeedbackService
)
