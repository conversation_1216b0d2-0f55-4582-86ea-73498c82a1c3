# 易盾验证码SDK集成文档

本文档介绍如何在gin-vue-admin项目中集成易盾验证码验证功能，使用易盾官方Golang SDK。

## 目录

- [功能特性](#功能特性)
- [快速开始](#快速开始)
- [配置说明](#配置说明)
- [API接口](#api接口)
- [SDK集成](#sdk集成)
- [前端集成](#前端集成)
- [使用示例](#使用示例)
- [中间件使用](#中间件使用)
- [常见问题](#常见问题)

## 功能特性

- ✅ **官方SDK集成**：使用易盾官方Golang SDK进行验证
- ✅ **原生结构体**：直接使用SDK的请求响应结构，无需转换
- ✅ **验证码二次校验**：支持滑块、点选等验证码类型的服务端验证
- ✅ **自动重试机制**：SDK内置重试和熔断机制
- ✅ **完整错误处理**：详细的错误信息和日志记录
- ✅ **工具类封装**：提供简化的工具类方法
- ✅ **中间件支持**：可作为Gin中间件使用
- ✅ **配置验证**：自动验证配置完整性
- ✅ **IP地址获取**：自动获取用户真实IP

## 快速开始

### 1. 获取易盾凭证

1. 登录 [易盾控制台](https://dun.163.com/)
2. 进入"验证码"产品页面
3. 创建验证码实例，获取以下信息：
   - **SecretId**: 访问密钥ID
   - **SecretKey**: 访问密钥Secret
   - **CaptchaId**: 验证码ID

### 2. 配置参数

在 `config.yaml` 中配置易盾参数：

```yaml
# 易盾配置
yidun:
  secret-id: "your_secret_id"        # 易盾访问密钥ID（SecretId）
  secret-key: "your_secret_key"      # 易盾访问密钥Secret（SecretKey）  
  captcha-id: "your_captcha_id"      # 易盾验证码ID
  region-code: "cn-hangzhou"         # 区域代码，默认杭州
  timeout: 10                       # 超时时间(秒)，默认10秒
  max-retry-count: 3                # 最大重试次数，默认3次
```

### 3. 验证配置

验证配置功能已移除，请确保配置正确。

## 配置说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| secret-id | string | ✅ | 易盾访问密钥ID |
| secret-key | string | ✅ | 易盾访问密钥Secret |
| captcha-id | string | ✅ | 验证码ID |
| region-code | string | ❌ | 区域代码，默认cn-hangzhou |
| timeout | int | ❌ | 超时时间，默认10秒 |
| max-retry-count | int | ❌ | 最大重试次数，默认3次 |

## API接口

### 验证验证码

**请求**
```http
POST /api/yidun/captcha/verify
Content-Type: application/json

{
  "captchaId": "your_captcha_id",  // 必填，验证码ID
  "validate": "验证数据",          // 必填，来自前端的验证数据
  "user": "user123",              // 可选，用户标识
  "type": "2",                    // 可选，验证码类型
  "userAgent": "Mozilla/5.0..."   // 可选，用户代理
}
```

**响应（SDK原生结构）**
```json
{
  "code": 0,
  "msg": "操作成功", 
  "data": {
    "error": 0,
    "msg": "success",
    "result": true,
    "token": "验证令牌",
    "captchaType": 1,
    "clientIp": "用户IP",
    "extraData": "额外数据",
    "phone": "手机号",
    "sdkReduce": "SDK优化信息",
    "clientUa": "用户代理"
  }
}
```

## SDK集成

### 依赖引入

项目已自动引入易盾官方SDK：

```go
import captchaSdk "github.com/yidun/yidun-golang-sdk/yidun/service/captcha"
```

### 数据结构

```go
// 请求DTO（用于API接收参数）
type CaptchaVerifyRequestDTO struct {
    CaptchaId string `json:"captchaId" binding:"required"` // 验证码ID
    Validate  string `json:"validate" binding:"required"`  // 验证数据
    User      string `json:"user"`                         // 用户标识
    Type      string `json:"type"`                         // 验证码类型
    UserAgent string `json:"userAgent"`                    // 用户代理
}

// 响应结构（直接使用SDK结构）
type CaptchaVerifyResponse = captchaSdk.CaptchaVerifyResponse
```

### SDK特性

- **验证功能**：使用官方SDK进行验证码验证
- **原生结构**：直接使用SDK的响应结构，保持完整性
- **自动重试**：SDK内置重试机制
- **熔断降级**：SDK支持熔断降级功能
- **多种签名算法**：支持MD5、SHA1、SHA256、SM3

## 前端集成

### HTML + JavaScript 示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>易盾验证码示例</title>
    <script src="https://c.dun.163yun.com/js/c.js"></script>
</head>
<body>
    <div id="captcha"></div>
    
    <script>
    // 初始化验证码
    window.NECaptcha.init({
        element: '#captcha',
        captchaId: 'your_captcha_id',
        mode: 'popup',
        width: 320,
        onVerify: async function(err, data) {
            if (err) {
                console.error('验证失败:', err);
                return;
            }
            
            // 调用后端验证接口
            const verifyResponse = await fetch('/api/yidun/captcha/verify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    captchaId: 'your_captcha_id',
                    validate: data.validate
                })
            });
            
            const verifyData = await verifyResponse.json();
            if (verifyData.code === 0 && verifyData.data.result) {
                alert('验证成功！');
                // 处理验证成功的逻辑
            } else {
                alert('验证失败！');
                // 处理验证失败的逻辑
            }
        }
    });
    </script>
</body>
</html>
```

## 使用示例

### 1. 直接使用Service

```go
package main

import (
    "context"
    "fmt"
    
    "github.com/flipped-aurora/gin-vue-admin/server/model/yidun"
    yidunService "github.com/flipped-aurora/gin-vue-admin/server/service/yidun"
)

func main() {
    service := &yidunService.YidunCaptchaService{}
    
    // 验证验证码
    verifyReq := &yidun.CaptchaVerifyRequestDTO{
        CaptchaId: "your_captcha_id",
        Validate:  "用户提交的验证数据",
        User:      "user123",
    }
    
    ctx := context.Background()
    verifyResp, err := service.VerifyCaptcha(ctx, verifyReq)
    if err != nil {
        fmt.Printf("验证失败: %v\n", err)
        return
    }
    
    // 使用SDK原生结构，需要检查指针
    if verifyResp.Error != nil && *verifyResp.Error == 0 && 
       verifyResp.Result != nil && *verifyResp.Result {
        fmt.Println("验证成功！")
    } else {
        errorMsg := "unknown error"
        if verifyResp.Msg != nil {
            errorMsg = *verifyResp.Msg
        }
        fmt.Printf("验证失败: %s\n", errorMsg)
    }
}
```

### 2. 使用工具类

```go
package main

import (
    "context"
    "fmt"
    
    "github.com/flipped-aurora/gin-vue-admin/server/utils/yidun"
)

func main() {
    // 创建工具类实例
    captchaUtils := yidun.NewCaptchaUtils()
    
    // 检查配置
    if !captchaUtils.IsConfigured() {
        fmt.Println("易盾未配置")
        return
    }
    
    ctx := context.Background()
    
    // 快速验证（工具类会处理指针检查）
    result, err := captchaUtils.QuickVerify(ctx, "用户提交的验证数据")
    if err != nil {
        fmt.Printf("验证失败: %v\n", err)
        return
    }
    
    if result {
        fmt.Println("验证成功！")
    } else {
        fmt.Println("验证失败！")
    }
}
```

### 3. 在Gin Handler中使用

```go
package main

import (
    "context"
    "net/http"
    
    "github.com/gin-gonic/gin"
    "github.com/flipped-aurora/gin-vue-admin/server/utils/yidun"
)

func loginHandler(c *gin.Context) {
    var req struct {
        Username     string `json:"username" binding:"required"`
        Password     string `json:"password" binding:"required"`
        CaptchaData  string `json:"captchaData" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // 验证验证码
    captchaUtils := yidun.NewCaptchaUtils()
    ctx := context.WithValue(c.Request.Context(), "gin", c)
    
    passed, err := captchaUtils.QuickVerify(ctx, req.CaptchaData)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "验证码验证失败"})
        return
    }
    
    if !passed {
        c.JSON(http.StatusBadRequest, gin.H{"error": "验证码验证失败"})
        return
    }
    
    // 继续处理登录逻辑...
    c.JSON(http.StatusOK, gin.H{"message": "登录成功"})
}
```

## 中间件使用

### 创建验证码中间件

```go
package middleware

import (
    "context"
    "net/http"
    
    "github.com/gin-gonic/gin"
    "github.com/flipped-aurora/gin-vue-admin/server/utils/yidun"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
)

// YidunCaptchaMiddleware 易盾验证码中间件
func YidunCaptchaMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 只对POST请求进行验证码验证
        if c.Request.Method != "POST" {
            c.Next()
            return
        }
        
        // 从请求头或请求体中获取验证码数据
        captchaData := c.GetHeader("X-Captcha-Data")
        if captchaData == "" {
            // 尝试从JSON请求体中获取
            var reqBody struct {
                CaptchaData string `json:"captchaData"`
            }
            if err := c.ShouldBindJSON(&reqBody); err == nil {
                captchaData = reqBody.CaptchaData
            }
        }
        
        if captchaData == "" {
            response.FailWithMessage("请提供验证码数据", c)
            c.Abort()
            return
        }
        
        // 验证验证码
        captchaUtils := yidun.NewCaptchaUtils()
        ctx := context.WithValue(context.Background(), "gin", c)
        
        passed, err := captchaUtils.QuickVerify(ctx, captchaData)
        if err != nil {
            response.FailWithMessage("验证码验证失败: "+err.Error(), c)
            c.Abort()
            return
        }
        
        if !passed {
            response.FailWithMessage("验证码验证失败", c)
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

## 常见问题

### Q1: SDK导入失败？
**A**: 确保已正确安装SDK依赖：
```bash
go get github.com/yidun/yidun-golang-sdk@v1.0.25
go mod tidy
```

### Q2: 为什么响应字段都是指针？
**A**: 这是SDK的设计，所有字段都是指针类型以区分零值和空值。在使用时需要检查指针是否为nil。

### Q3: 如何正确处理SDK响应？
**A**: 示例代码：
```go
if response.Error != nil && *response.Error == 0 && 
   response.Result != nil && *response.Result {
    // 验证成功
} else {
    // 验证失败
}
```

### Q4: 验证总是失败？
**A**: 请检查：
1. 配置是否正确（SecretId、SecretKey、CaptchaId）
2. 验证数据是否完整
3. 网络连接是否正常
4. 查看日志中的详细错误信息

### Q5: 如何获取用户IP？
**A**: 系统会自动从Gin Context中获取用户IP，支持代理和负载均衡场景。

### Q6: 支持哪些验证码类型？
**A**: 支持易盾的所有验证码类型：
- 滑块验证码（type: "2"）
- 点选验证码（type: "3"）
- 短信验证码（type: "4"）
- 其他类型请参考易盾官方文档

### Q7: 如何处理并发请求？
**A**: SDK内置了连接池和并发处理机制，可以安全地处理并发请求。建议复用service实例而不是频繁创建。

### Q8: 如何调试验证码问题？
**A**: 
1. 启用详细日志记录
2. 检查所有指针字段是否为nil
3. 在易盾控制台查看调用日志
4. 检查网络和防火墙设置

---

更多信息请参考：
- [易盾官方文档](https://support.dun.163.com/)
- [易盾Golang SDK](https://github.com/yidun/yidun-golang-sdk)
- [gin-vue-admin官方文档](https://www.gin-vue-admin.com/) 