package mcp

import api "github.com/flipped-aurora/gin-vue-admin/server/api/v1"

type RouterGroup struct {
	ProjectToolsRouter
	ProjectsRouter
	McpClientRouter
	ApiKeyRouter
	UseCaseRouter
	ToolsRouter
	MapCoordinatesRouter
	BindingRouter
}

var (
	projectToolsApi   = api.ApiGroupApp.McpApiGroup.ProjectToolsApi
	projectsApi       = api.ApiGroupApp.McpApiGroup.ProjectsApi
	mcpClientApi      = api.ApiGroupApp.McpApiGroup.McpClientApi
	apiKeyApi         = api.ApiGroupApp.McpApiGroup.ApiKeyApi
	useCaseApi        = api.ApiGroupApp.McpApiGroup.UseCaseApi
	mapCoordinatesApi = api.ApiGroupApp.McpApiGroup.MapCoordinatesApi
	bindingApi        = api.ApiGroupApp.McpApiGroup.BindingApi
)
