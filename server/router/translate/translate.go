package translate

import (
	"github.com/gin-gonic/gin"
)

type TranslateRouter struct{}

func (s *TranslateRouter) InitTranslateRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	//dleware.OperationRecord())
	translateGroupWithoutAuth := PublicRouter.Group("translate")
	{
		translateGroupWithoutAuth.POST("/baiduTranslate", translateApi.TranslateHandler)
		translateGroupWithoutAuth.GET("/languages", translateApi.ListLanguagesHandler)
	}
}
