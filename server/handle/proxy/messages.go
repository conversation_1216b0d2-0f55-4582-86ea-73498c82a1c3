package proxy

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"log"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	mcprouter "github.com/flipped-aurora/gin-vue-admin/server/model/mcprouter"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/jsonrpc"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/mcpclient"
	"github.com/flipped-aurora/gin-vue-admin/server/service/mcprouter/proxy"
	"github.com/labstack/echo/v4"
)

// Messages is a handler for the messages endpoint
func Messages(c echo.Context) error {
	var err error
	ctx := proxy.GetSSEContext(c)
	if ctx == nil {
		return c.String(http.StatusInternalServerError, "Failed to get SSE context")
	}

	// 新增：优先通过sessionid查找第三方messages地址
	sessionID := c.QueryParam("sessionid")
	if sessionID == "" {
		sessionID = c.FormValue("sessionid")
	}
	if sessionID == "" {
		sessionID = c.Param("sessionid")
	}
	if sessionID != "" && ctx != nil {
		session := ctx.GetSession(sessionID)
		if session != nil && session.ThirdPartyMessagesURL != "" {
			// ====== 积分校验逻辑（第三方代理前，预检查） ======
			var shouldDeductPoints bool
			var toolID string
			var userID uint64
			var toolRecord mcp.ProjectTools
			request, _ := ctx.GetJSONRPCRequest()
			if request != nil && request.Method == "tools/call" {
				if paramsMap, ok := request.Params.(map[string]interface{}); ok {
					if name, ok := paramsMap["name"].(string); ok {
						toolID = name
					}
				}
				userIDStr := session.ProxyInfo().UserID
				if userIDStr == "" {
					// userID未存储，跳过积分校验
				} else {
					userID, _ = strconv.ParseUint(userIDStr, 10, 64)
					if userID > 0 && toolID != "" {
						var project mcp.Projects
						if err := global.GVA_DB.Where("uuid = ?", session.ProxyInfo().ServerUUID).First(&project).Error; err != nil {
							return c.String(http.StatusForbidden, "项目不存在")
						}
						if err := global.GVA_DB.Where("project_id = ? AND name = ?", project.ID, toolID).First(&toolRecord).Error; err != nil {
							return c.String(http.StatusForbidden, "工具不存在")
						}
						var user system.SysUser
						if err := global.GVA_DB.Where("id = ?", userID).First(&user).Error; err != nil {
							return c.String(http.StatusForbidden, "用户不存在")
						}
						if user.FreePoints+user.Points < *toolRecord.Points {
							return c.String(http.StatusForbidden, "积分不足")
						}
						shouldDeductPoints = true
					}
				}
			}

			// 设置 proxyInfo 的积分信息（第三方代理）
			currentProxyInfo := session.ProxyInfo()
			if toolID != "" && toolRecord.Points != nil {
				currentProxyInfo.ToolName = toolID
				currentProxyInfo.Points = *toolRecord.Points
				session.SetProxyInfo(currentProxyInfo)
			}

			// ====== 继续第三方代理逻辑 ======
			thirdPartyUrl := session.ThirdPartyMessagesURL
			if session.ThirdPartyQueryRaw != "" {
				if strings.Contains(thirdPartyUrl, "?") {
					thirdPartyUrl += "&" + session.ThirdPartyQueryRaw
				} else {
					thirdPartyUrl += "?" + session.ThirdPartyQueryRaw
				}
			}
			log.Printf("最终代理到第三方的URL: %s", thirdPartyUrl)
			requestBody, _ := json.Marshal(request)
			reqProxy, _ := http.NewRequest("POST", thirdPartyUrl, strings.NewReader(string(requestBody)))
			// 复制原始请求header
			for k, v := range c.Request().Header {
				if len(v) > 0 {
					reqProxy.Header.Set(k, v[0])
				}
			}
			// 添加DbEnv中的键值对到请求头
			dbEnv := session.ProxyInfo().DbEnv
			if dbEnv != nil && len(dbEnv) > 0 {
				for key, value := range dbEnv {
					reqProxy.Header.Set(key, value)
					log.Printf("添加DbEnv到请求头: %s = %s", key, value)
				}
			}

			client := &http.Client{Timeout: 0}
			resp, err := client.Do(reqProxy)
			if err != nil {
				return c.String(http.StatusBadGateway, "SSE代理失败")
			}
			defer resp.Body.Close()
			c.Response().Header().Set("Content-Type", "application/json")

			// 收集完整响应数据用于积分扣除判断
			var fullResponse []byte
			buf := make([]byte, 4096)
			for {
				n, err := resp.Body.Read(buf)
				if n > 0 {
					fullResponse = append(fullResponse, buf[:n]...)
					c.Response().Write(buf[:n])
					c.Response().Flush()
					log.Printf("代理第三方messages收到数据: %d bytes", n)
				}
				if err != nil {
					if err == io.EOF {
						log.Printf("第三方messages读取完毕（EOF），正常结束")
					} else {
						log.Printf("代理第三方messages读取出错: %v", err)
					}
					break
				}
			}

			// 检查响应并决定是否扣除积分
			if shouldDeductPoints && userID > 0 && toolID != "" {
				var response map[string]interface{}
				if err := json.Unmarshal(fullResponse, &response); err == nil {
					if resultData, ok := response["result"].(map[string]interface{}); ok {
						// 检查是否有isError字段且为true
						if isError, exists := resultData["isError"]; !exists || isError != true {
							// 没有错误，扣除积分
							err := service.ServiceGroupApp.McpServiceGroup.ProjectToolsService.UseToolWithPoints(c.Request().Context(), uint(userID), fmt.Sprintf("%d", toolRecord.ID))
							if err != nil {
								log.Printf("第三方代理扣积分失败: %v", err)
								// 不返回错误，因为工具调用已经成功
							} else {
								log.Printf("第三方代理扣积分成功: userID=%d, toolID=%s", userID, toolID)
							}
						} else {
							log.Printf("第三方代理工具调用有错误，不扣积分: userID=%d, toolID=%s", userID, toolID)
						}
					}
				}
			}

			return nil
		}
	}

	// 直接获取key和token参数，兼容query、form和路径参数
	key := c.QueryParam("key")
	token := c.QueryParam("token")
	if key == "" {
		key = c.FormValue("key")
	}
	if token == "" {
		token = c.FormValue("token")
	}
	if key == "" {
		key = c.Param("key")
	}
	if token == "" {
		token = c.Param("token")
	}

	// 查serverkey
	serverkey, err := mcprouter.FindServerkeyByServerKey(key)
	if err == nil && serverkey.SseUrl != "" {
		// 查api_key
		_, err := service.ServiceGroupApp.McpServiceGroup.ApiKeyService.ValidateApiKey(token)
		if err != nil {
			// 直接代理到远端/messages
			remoteMessagesUrl := strings.Replace(serverkey.SseUrl, "/sse", "/messages", 1)
			headers := http.Header{}
			if serverkey.EnvJson != "" {
				var env map[string]interface{}
				_ = json.Unmarshal([]byte(serverkey.EnvJson), &env)
				for k, v := range env {
					headers.Set(k, fmt.Sprintf("%v", v))
				}
			}
			headers.Set("X-Token", token)
			request, _ := ctx.GetJSONRPCRequest()
			requestBody, _ := json.Marshal(request)
			reqProxy, _ := http.NewRequest("POST", remoteMessagesUrl, strings.NewReader(string(requestBody)))
			reqProxy.Header = headers
			for k, v := range c.Request().Header {
				if len(v) > 0 {
					reqProxy.Header.Set(k, v[0])
				}
			}
			// 添加DbEnv中的键值对到请求头（如果存在session）
			if sessionID != "" {
				if ctx != nil {
					session := ctx.GetSession(sessionID)
					if session != nil {
						dbEnv := session.ProxyInfo().DbEnv
						if dbEnv != nil && len(dbEnv) > 0 {
							for key, value := range dbEnv {
								reqProxy.Header.Set(key, value)
								log.Printf("添加DbEnv到请求头: %s = %s", key, value)
							}
						}
					}
				}
			}
			log.Printf("[MESSAGES代理-直接token兜底] 请求url: %s, headers: %+v", remoteMessagesUrl, reqProxy.Header)
			client := &http.Client{Timeout: 0}
			resp, err := client.Do(reqProxy)
			if err != nil {
				return c.String(http.StatusBadGateway, "SSE代理失败")
			}
			defer resp.Body.Close()
			c.Response().Header().Set("Content-Type", "application/json")
			buf := make([]byte, 4096)
			for {
				n, err := resp.Body.Read(buf)
				if n > 0 {
					c.Response().Write(buf[:n])
					c.Response().Flush()
					if strings.Contains(string(buf[:n]), "event: done") {
						break
					}
				}
				if err != nil {
					break
				}
			}
			return nil
		}
	}

	sessionID = ctx.QueryParam("sessionid")
	if sessionID == "" {
		return ctx.JSONRPCError(jsonrpc.ErrorInvalidParams, nil)
	}

	session := ctx.GetSession(sessionID)
	if session == nil {
		return ctx.JSONRPCError(jsonrpc.ErrorInvalidParams, nil)
	}

	// 本地messages逻辑前的积分预检查
	var shouldDeductPointsLocal bool
	var toolIDLocal string
	var userIDLocal uint64
	var toolRecordLocal mcp.ProjectTools
	request, err := ctx.GetJSONRPCRequest()
	if err != nil {
		return ctx.JSONRPCError(jsonrpc.ErrorParseError, nil)
	}
	if request.Method == "tools/call" {
		if paramsMap, ok := request.Params.(map[string]interface{}); ok {
			if name, ok := paramsMap["name"].(string); ok {
				toolIDLocal = name
			}
		}
		userIDStr := session.ProxyInfo().UserID
		if userIDStr == "" {
			// userID未存储，跳过积分校验
		} else {
			userIDLocal, _ = strconv.ParseUint(userIDStr, 10, 64)
			if userIDLocal > 0 && toolIDLocal != "" {
				var project mcp.Projects
				if err := global.GVA_DB.Where("uuid = ?", session.ProxyInfo().ServerUUID).First(&project).Error; err != nil {
					return c.String(http.StatusForbidden, "项目不存在")
				}
				if err := global.GVA_DB.Where("project_id = ? AND name = ?", project.ID, toolIDLocal).First(&toolRecordLocal).Error; err != nil {
					return c.String(http.StatusForbidden, "工具不存在")
				}
				var user system.SysUser
				if err := global.GVA_DB.Where("id = ?", userIDLocal).First(&user).Error; err != nil {
					return c.String(http.StatusForbidden, "用户不存在")
				}
				if user.FreePoints+user.Points < *toolRecordLocal.Points {
					return c.String(http.StatusForbidden, "积分不足")
				}
				shouldDeductPointsLocal = true
			}
		}
	}

	proxyInfo := session.ProxyInfo()
	sseKey := session.Key()
	proxyInfo.ToolName = toolIDLocal
	// 设置积分信息（复用前面查询的工具信息）
	if toolRecordLocal.Points != nil {
		proxyInfo.Points = *toolRecordLocal.Points
	}
	proxyInfo.JSONRPCVersion = request.JSONRPC
	proxyInfo.RequestMethod = request.Method
	proxyInfo.RequestTime = time.Now()
	proxyInfo.RequestParams = request.Params

	if request.ID != nil {
		proxyInfo.RequestID = request.ID
	}

	if request.Method == "initialize" {
		paramsB, _ := json.Marshal(request.Params)
		params := &jsonrpc.InitializeParams{}
		if err := json.Unmarshal(paramsB, params); err != nil {
			return ctx.JSONRPCError(jsonrpc.ErrorParseError, nil)
		}

		proxyInfo.ClientName = params.ClientInfo.Name
		proxyInfo.ClientVersion = params.ClientInfo.Version
		proxyInfo.ProtocolVersion = params.ProtocolVersion

		session.SetProxyInfo(proxyInfo)
		ctx.StoreSession(sessionID, session)
	}

	// 进程复用逻辑，与mcp.go保持一致
	var client mcpclient.Client
	serverConfig := session.ServerConfig()
	log.Printf("[Messages进程复用] ShareProcess=%t, key=%s", serverConfig.ShareProcess, sseKey)

	if serverConfig.ShareProcess {
		// 复用已有 client/进程
		client = ctx.GetClient(sseKey)
		if client == nil {
			// 检查session中是否已有客户端
			if session.Client() != nil {
				client = session.Client()
				ctx.StoreClient(sseKey, client)
				log.Printf("[Messages进程复用] 从session复用客户端: key=%s", sseKey)
			} else {
				_client, err := mcpclient.NewClient(serverConfig)
				if err != nil {
					fmt.Printf("connect to mcp server failed: %v\n", err)
					return ctx.JSONRPCError(jsonrpc.ErrorProxyError, request.ID)
				}
				if err := _client.Error(); err != nil {
					fmt.Printf("mcp server run failed: %v\n", err)
					return ctx.JSONRPCError(jsonrpc.ErrorProxyError, request.ID)
				}
				ctx.StoreClient(sseKey, _client)
				session.SetClient(_client)
				client = _client
				log.Printf("[Messages进程复用] 创建新客户端并存储: key=%s", sseKey)

				client.OnNotification(func(message []byte) {
					fmt.Printf("received notification: %s\n", message)
					session.SendMessage(string(message))
				})
			}
		} else {
			log.Printf("[Messages进程复用] 复用已有客户端: key=%s", sseKey)
		}
	} else {
		// 不复用，每次都新建
		log.Printf("[Messages进程复用] ShareProcess=false，创建新客户端: key=%s", sseKey)
		client, err = mcpclient.NewClient(serverConfig)
		if err != nil {
			fmt.Printf("connect to mcp server failed: %v\n", err)
			return ctx.JSONRPCError(jsonrpc.ErrorProxyError, request.ID)
		}
		if err := client.Error(); err != nil {
			fmt.Printf("mcp server run failed: %v\n", err)
			return ctx.JSONRPCError(jsonrpc.ErrorProxyError, request.ID)
		}
		client.OnNotification(func(message []byte) {
			fmt.Printf("received notification: %s\n", message)
			session.SendMessage(string(message))
		})
		log.Printf("[Messages进程复用] ShareProcess=false，创建新客户端: key=%s", sseKey)
		// 不存 ctx.StoreClient
	}

	if client == nil {
		return ctx.JSONRPCError(jsonrpc.ErrorProxyError, request.ID)
	}

	response, err := client.ForwardMessage(request)
	if err != nil {
		fmt.Printf("forward message failed: %v\n", err)
		// 只有在ShareProcess=false或者客户端确实出错时才关闭
		if !serverConfig.ShareProcess {
			session.Close()
		} else {
			// ShareProcess=true时，清理存储的客户端，但不关闭session
			ctx.DeleteClient(sseKey)
			session.SetClient(nil)
			log.Printf("[Messages进程复用] 清理出错的共享客户端: key=%s", sseKey)
		}
		return ctx.JSONRPCError(jsonrpc.ErrorProxyError, request.ID)
	}

	if response != nil {
		if request.Method == "initialize" && response.Result != nil {
			resultB, _ := json.Marshal(response.Result)
			result := &jsonrpc.InitializeResult{}
			if err := json.Unmarshal(resultB, result); err != nil {
				fmt.Printf("unmarshal initialize result failed: %v\n", err)
				return ctx.JSONRPCError(jsonrpc.ErrorParseError, request.ID)
			}

			proxyInfo.ServerName = result.ServerInfo.Name
			proxyInfo.ServerVersion = result.ServerInfo.Version

			session.SetProxyInfo(proxyInfo)
			ctx.StoreSession(sessionID, session)

			// 发送 notifications/initialized 通知
			go func() {
				time.Sleep(100 * time.Millisecond) // 短暂延迟确保 initialize 响应已发送
				notificationReq := &jsonrpc.Request{
					BaseRequest: jsonrpc.BaseRequest{
						JSONRPC: jsonrpc.JSONRPC_VERSION,
						Method:  jsonrpc.MethodInitializedNotification,
					},
					Params: map[string]interface{}{},
				}
				_, err := client.ForwardMessage(notificationReq)
				if err != nil {
					log.Printf("发送 notifications/initialized 失败: %v", err)
				} else {
					log.Printf("已发送 notifications/initialized 通知")
				}
			}()
		}

		// not notification message, send sse message
		session.SendMessage(response.String())
	}

	proxyInfo.ResponseResult = response

	proxyInfo.ResponseTime = time.Now()
	costTime := proxyInfo.ResponseTime.Sub(proxyInfo.RequestTime)
	proxyInfo.CostTime = costTime.Milliseconds()

	// 检查响应并决定是否扣除积分
	if shouldDeductPointsLocal && userIDLocal > 0 && toolIDLocal != "" {
		shouldDeduct := true
		if response != nil && response.Result != nil {
			if resultBytes, err := json.Marshal(response.Result); err == nil {
				var callResult map[string]interface{}
				if err := json.Unmarshal(resultBytes, &callResult); err == nil {
					if isError, exists := callResult["isError"]; exists && isError == true {
						shouldDeduct = false
						log.Printf("本地messages工具调用有错误，不扣积分: userID=%d, toolID=%s", userIDLocal, toolIDLocal)
					}
				}
			}
		}

		if shouldDeduct {
			err := service.ServiceGroupApp.McpServiceGroup.ProjectToolsService.UseToolWithPoints(c.Request().Context(), uint(userIDLocal), fmt.Sprintf("%d", toolRecordLocal.ID))
			if err != nil {
				log.Printf("本地messages扣积分失败: %v", err)
				// 不返回错误，因为工具调用已经完成
			} else {
				log.Printf("本地messages扣积分成功: userID=%d, toolID=%s", userIDLocal, toolIDLocal)
			}
		}
	}

	proxyInfoB, _ := json.Marshal(proxyInfo)

	fmt.Printf("proxyInfo: %s\n", string(proxyInfoB))

	return ctx.JSONRPCResponse(response)
}
