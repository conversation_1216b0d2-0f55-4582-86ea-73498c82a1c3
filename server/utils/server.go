package utils

import (
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"
)

const (
	B  = 1
	KB = 1024 * B
	MB = 1024 * KB
	GB = 1024 * MB
)

type Server struct {
	Os   Os     `json:"os"`
	Cpu  Cpu    `json:"cpu"`
	Ram  Ram    `json:"ram"`
	Disk []Disk `json:"disk"`
}

type Os struct {
	GOOS         string `json:"goos"`
	NumCPU       int    `json:"numCpu"`
	Compiler     string `json:"compiler"`
	GoVersion    string `json:"goVersion"`
	NumGoroutine int    `json:"numGoroutine"`
}

type Cpu struct {
	Cpus  []float64 `json:"cpus"`
	Cores int       `json:"cores"`
}

type Ram struct {
	UsedMB      int `json:"usedMb"`
	TotalMB     int `json:"totalMb"`
	UsedPercent int `json:"usedPercent"`
}

type Disk struct {
	MountPoint  string `json:"mountPoint"`
	UsedMB      int    `json:"usedMb"`
	UsedGB      int    `json:"usedGb"`
	TotalMB     int    `json:"totalMb"`
	TotalGB     int    `json:"totalGb"`
	UsedPercent int    `json:"usedPercent"`
}

//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: InitCPU
//@description: OS信息
//@return: o Os, err error

func InitOS() (o Os) {
	o.GOOS = runtime.GOOS
	o.NumCPU = runtime.NumCPU()
	o.Compiler = runtime.Compiler
	o.GoVersion = runtime.Version()
	o.NumGoroutine = runtime.NumGoroutine()
	return o
}

//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: InitCPU
//@description: CPU信息
//@return: c Cpu, err error

func InitCPU() (c Cpu, err error) {
	if cores, err := cpu.Counts(false); err != nil {
		return c, err
	} else {
		c.Cores = cores
	}
	if cpus, err := cpu.Percent(time.Duration(200)*time.Millisecond, true); err != nil {
		return c, err
	} else {
		c.Cpus = cpus
	}
	return c, nil
}

//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: InitRAM
//@description: RAM信息
//@return: r Ram, err error

func InitRAM() (r Ram, err error) {
	if u, err := mem.VirtualMemory(); err != nil {
		return r, err
	} else {
		r.UsedMB = int(u.Used) / MB
		r.TotalMB = int(u.Total) / MB
		r.UsedPercent = int(u.UsedPercent)
	}
	return r, nil
}

//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: InitDisk
//@description: 硬盘信息
//@return: d Disk, err error

func InitDisk() (d []Disk, err error) {
	for i := range global.GVA_CONFIG.DiskList {
		mp := global.GVA_CONFIG.DiskList[i].MountPoint
		if u, err := disk.Usage(mp); err != nil {
			return d, err
		} else {
			d = append(d, Disk{
				MountPoint:  mp,
				UsedMB:      int(u.Used) / MB,
				UsedGB:      int(u.Used) / GB,
				TotalMB:     int(u.Total) / MB,
				TotalGB:     int(u.Total) / GB,
				UsedPercent: int(u.UsedPercent),
			})
		}
	}
	return d, nil
}

// FindProcessPIDsByServerKey 查找包含 serverKey 的进程 PID 列表
// 同时尝试普通Linux系统和Docker容器两种方式
func FindProcessPIDsByServerKey(serverKey string) ([]int, error) {
	var allPids []int

	// 方式1: 普通Linux系统查找方式
	cmd1 := exec.Command("bash", "-c", "ps -ef | grep '"+serverKey+"' | grep -v grep | awk '{print $2}'")
	output1, err1 := cmd1.Output()
	if err1 == nil {
		for _, line := range strings.Split(string(output1), "\n") {
			if line = strings.TrimSpace(line); line != "" {
				if pid, err := strconv.Atoi(line); err == nil {
					allPids = append(allPids, pid)
				}
			}
		}
	}

	// 方式2: Docker容器查找方式，通常主进程是PID 1
	cmd2 := exec.Command("bash", "-c", "ps aux | grep '"+serverKey+"' | grep -v grep | awk '{print $2}'")
	output2, err2 := cmd2.Output()
	if err2 == nil {
		for _, line := range strings.Split(string(output2), "\n") {
			if line = strings.TrimSpace(line); line != "" {
				if pid, err := strconv.Atoi(line); err == nil {
					// 避免重复添加相同的PID
					exists := false
					for _, existPid := range allPids {
						if existPid == pid {
							exists = true
							break
						}
					}
					if !exists {
						allPids = append(allPids, pid)
					}
				}
			}
		}
	}

	// 如果两种方式都失败了，返回错误
	if err1 != nil && err2 != nil {
		return nil, err1
	}

	return allPids, nil
}

// KillProcess 杀掉指定 PID
// 尝试两种方式kill进程，适用于普通Linux系统和Docker容器
func KillProcess(pid int) error {
	pidStr := strconv.Itoa(pid)

	// 方式1: 使用 kill -9
	cmd1 := exec.Command("kill", "-9", pidStr)
	err1 := cmd1.Run()

	// 方式2: 使用 pkill（适用于某些容器环境）
	cmd2 := exec.Command("pkill", "-9", "-f", pidStr)
	err2 := cmd2.Run()

	// 如果至少有一种方式成功，就认为成功
	if err1 == nil || err2 == nil {
		return nil
	}

	// 两种方式都失败了，返回第一个错误
	return err1
}
