#!/bin/bash

# Gin-Vue-Admin 启动脚本
# 用法: ./start.sh [service] [environment]
# service: main|proxy (默认: main)
# environment: dev|prod (默认: 本地环境)

SERVICE=${1:-main}
ENV=${2:-}

echo "=== Gin-Vue-Admin 启动脚本 ==="
echo "服务类型: $SERVICE"
echo "环境: ${ENV:-本地环境}"
echo "================================"

case $SERVICE in
    "main")
        if [ -n "$ENV" ]; then
            echo "启动主服务 (环境: $ENV)..."
            go run main.go --env=$ENV
        else
            echo "启动主服务 (本地环境)..."
            go run main.go
        fi
        ;;
    "proxy")
        if [ -n "$ENV" ]; then
            echo "启动代理服务 (环境: $ENV)..."
            go run main.go proxy --env=$ENV
        else
            echo "启动代理服务 (本地环境)..."
            go run main.go proxy
        fi
        ;;
    *)
        echo "错误: 未知的服务类型 '$SERVICE'"
        echo "支持的服务类型: main, proxy"
        echo ""
        echo "使用方法:"
        echo "  ./start.sh main dev      # 启动主服务 (开发环境)"
        echo "  ./start.sh main prod     # 启动主服务 (生产环境)"
        echo "  ./start.sh proxy dev     # 启动代理服务 (开发环境)"
        echo "  ./start.sh proxy prod    # 启动代理服务 (生产环境)"
        echo "  ./start.sh main          # 启动主服务 (本地环境)"
        echo "  ./start.sh proxy         # 启动代理服务 (本地环境)"
        exit 1
        ;;
esac
