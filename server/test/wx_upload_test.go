package test

import (
	"testing"

	"github.com/flipped-aurora/gin-vue-admin/server/core"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/initialize"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"go.uber.org/zap"
)

// 初始化测试环境
func init() {
	// 直接指定配置文件路径，避免flag冲突
	global.GVA_VP = core.Viper("../config.yaml") // 读取配置文件
	global.GVA_LOG = core.Zap()               // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)

	// 初始化其他组件
	initialize.OtherInit()

	// 初始化数据库连接
	global.GVA_DB = initialize.Gorm()
	if global.GVA_DB != nil {
		initialize.RegisterTables() // 初始化表
	}
}

func TestDownloadAndUploadWechatAvatar(t *testing.T) {
	// 调用AuthProviderService的DownloadAndUploadWechatAvatar方法
	authProviderService := service.ServiceGroupApp.SystemServiceGroup.AuthProviderService
	imageUrl, err := authProviderService.DownloadAndUploadWechatAvatar("https://thirdwx.qlogo.cn/mmopen/vi_32/KMhESiaQOl479MKjB61JHxMFMUicNZ0yPTvKuVGa4gM0yBtt9TLZjVJibfdBfdibQRlcbvia5YgwNDmLKoLBkRaL8axJ5rBs26o63Ibuh3AeAVW0/132")
	if err != nil {
		t.Error(err)
		return
	}
	t.Log("上传成功，图片URL:", imageUrl)
}
