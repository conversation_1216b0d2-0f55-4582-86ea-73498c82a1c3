package initialize

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/config/pansou"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/plugin"
	pansouService "github.com/flipped-aurora/gin-vue-admin/server/service/pansou"
	pansouUtils "github.com/flipped-aurora/gin-vue-admin/server/utils/pansou"
)

// 全局 PanSou 搜索服务实例
var PanSouSearchService *pansouService.SearchService

// API设置函数，用于避免循环导入
var apiSetterFunc func(*pansouService.SearchService)

// InitPanSou 初始化 PanSou 搜索服务
func InitPanSou() error {
	// 初始化 PanSou 配置
	pansou.Init()

	// 初始化 HTTP 客户端
	pansouUtils.InitHTTPClient(pansou.AppConfig)

	// 创建插件管理器
	pluginManager := plugin.NewPluginManager()

	// 注册所有全局插件
	pluginManager.RegisterAllGlobalPlugins()

	// 初始化搜索服务
	searchService := pansouService.NewSearchService(pluginManager)

	// 保存全局实例
	PanSouSearchService = searchService

	// 设置到API层的全局实例（避免循环导入）
	// 这里通过函数指针的方式在运行时设置，避免编译时循环依赖
	if apiSetterFunc != nil {
		apiSetterFunc(searchService)
	}

	fmt.Println("✅ PanSou 搜索服务初始化完成")
	return nil
}

// GetPanSouSearchService 获取 PanSou 搜索服务实例
func GetPanSouSearchService() *pansouService.SearchService {
	return PanSouSearchService
}

// SetAPISetterFunc 设置API设置函数，用于避免循环导入
func SetAPISetterFunc(setter func(*pansouService.SearchService)) {
	apiSetterFunc = setter
}
