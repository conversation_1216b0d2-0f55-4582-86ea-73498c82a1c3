package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/device"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

func bizModel() error {
	db := global.GVA_DB
	err := db.AutoMigrate(mcp.ProjectTools{}, mcp.Projects{}, mcp.McpClient{}, mcp.ApiKey{}, device.Device{}, device.DeviceReport{}, system.SysVersion{})
	if err != nil {
		return err
	}
	return nil
}
