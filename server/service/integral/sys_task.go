package integral

import (
	"errors"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
	pointsRes "github.com/flipped-aurora/gin-vue-admin/server/model/integral/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type TaskService struct{}

var TaskServiceApp = new(TaskService)

// GetTaskList 获取任务列表（带用户完成状态）
func (taskService *TaskService) GetTaskList(userID uint) (list []pointsRes.TaskListItem, err error) {
	db := global.GVA_DB.Model(&integral.SysTask{})
	// 获取任务列表
	var tasks []integral.SysTask
	err = db.Order("sort ASC, id ASC").Find(&tasks).Error
	if err != nil {
		return
	}

	// 为每个任务查询用户完成状态
	list = make([]pointsRes.TaskListItem, len(tasks))
	for i, task := range tasks {
		item := pointsRes.TaskListItem{
			SysTask: task,
		}

		//当taskId=TASK_DAILY_SIGNIN时,item

		// 查询累计完成次数和总积分
		var userTasks []integral.SysUserTask
		global.GVA_DB.Where("user_id = ? AND task_id = ? AND status = ?",
			userID, task.ID, integral.UserTaskStatusCompleted).
			Order("created_at DESC").Find(&userTasks)

		item.TotalCompletedTimes = len(userTasks)
		for _, ut := range userTasks {
			item.TotalRewardPoints += ut.Reward
		}

		// 最后完成时间
		if len(userTasks) > 0 {
			item.LastCompletedAt = &userTasks[0].CreatedAt
		}

		list[i] = item
	}

	// 添加消耗体力任务项
	consumedPoints, err := taskService.getUserConsumedPoints(userID)
	if err != nil {
		// 如果查询失败，记录日志但不影响主要功能
		global.GVA_LOG.Error("查询用户消耗积分失败", zap.Error(err))
		consumedPoints = 0
	}

	// 创建消耗体力任务项
	consumeTaskItem := pointsRes.TaskListItem{
		SysTask: integral.SysTask{
			Title:       "消耗体力",
			Description: "用户消耗的体力统计",
			TaskType:    "consume",
			IsEnabled:   false, // 不可操作
			Sort:        9999,  // 排在最后
			Icon:        "⚡",
			Value:       "CONSUME",
		},
		TotalCompletedTimes: 0,
		TotalRewardPoints:   consumedPoints, // 显示为负数
	}

	// 将消耗体力项添加到列表末尾
	list = append(list, consumeTaskItem)

	return list, nil
}

// getUserConsumedPoints 获取用户消耗的积分总数
func (taskService *TaskService) getUserConsumedPoints(userID uint) (int, error) {
	var totalConsumed int

	// 查询所有负数的积分变动记录（消耗记录）
	err := global.GVA_DB.Model(&integral.SysUserPoints{}).
		Where("user_id = ? AND `change` < 0", userID).
		Select("COALESCE(SUM(`change`), 0)").
		Scan(&totalConsumed).Error

	return totalConsumed, err
}

// CompleteTask 完成任务
func (taskService *TaskService) CompleteTask(userID uint, taskID uint) (response pointsRes.CompleteTaskResponse, err error) {
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 查询任务信息
		var task integral.SysTask
		if err := tx.Where("id = ? AND is_enabled = ?", taskID, true).First(&task).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.New("任务不存在或已禁用")
			}
			return fmt.Errorf("查询任务失败: %v", err)
		}

		// 检查任务是否可以完成
		canComplete, reason, err := taskService.checkTaskCanComplete(tx, userID, task)
		if err != nil {
			return fmt.Errorf("检查任务状态失败: %v", err)
		}
		if !canComplete {
			return errors.New(reason)
		}

		// 记录任务完成
		userTask := integral.SysUserTask{
			UserID: userID,
			TaskID: taskID,
			Reward: task.Reward,
			Status: integral.UserTaskStatusCompleted,
			Remark: "用户完成任务",
		}
		if err := tx.Create(&userTask).Error; err != nil {
			return fmt.Errorf("记录任务完成失败: %v", err)
		}

		// 更新用户积分
		if err := tx.Model(&system.SysUser{}).Where("id = ?", userID).
			UpdateColumn("points", gorm.Expr("points + ?", task.Reward)).Error; err != nil {
			return fmt.Errorf("更新用户积分失败: %v", err)
		}

		// 记录积分流水
		pointsRecord := integral.SysUserPoints{
			UserID:     userID,
			Change:     task.Reward,
			Reason:     fmt.Sprintf("完成任务：%s", task.Title),
			Type:       "task",
			TaskID:     taskID,
			UserTaskID: userTask.ID,
		}
		if err := tx.Create(&pointsRecord).Error; err != nil {
			return fmt.Errorf("记录积分流水失败: %v", err)
		}

		// 构建响应
		response = pointsRes.CompleteTaskResponse{
			TaskID:       taskID,
			TaskTitle:    task.Title,
			RewardPoints: task.Reward,
			Message:      fmt.Sprintf("恭喜您完成任务「%s」，获得 %d 体力！", task.Title, task.Reward),
		}

		return nil
	})
	return response, err
}

// checkTaskCanComplete 检查任务是否可以完成
func (taskService *TaskService) checkTaskCanComplete(tx *gorm.DB, userID uint, task integral.SysTask) (canComplete bool, reason string, err error) {
	now := time.Now()

	switch task.TaskType {
	case integral.TaskTypeDaily:
		// 日常任务：检查今日是否已完成
		today := now.Truncate(24 * time.Hour)
		var count int64
		err = tx.Model(&integral.SysUserTask{}).
			Where("user_id = ? AND task_id = ? AND created_at >= ? AND status = ?",
				userID, task.ID, today, integral.UserTaskStatusCompleted).
			Count(&count).Error
		if err != nil {
			return false, "", err
		}
		if count > 0 {
			return false, "今日已完成该任务", nil
		}

	case integral.TaskTypeWeekly:
		// 每周任务：检查本周是否已完成
		weekStart := now.AddDate(0, 0, -int(now.Weekday())).Truncate(24 * time.Hour)
		var count int64
		err = tx.Model(&integral.SysUserTask{}).
			Where("user_id = ? AND task_id = ? AND created_at >= ? AND status = ?",
				userID, task.ID, weekStart, integral.UserTaskStatusCompleted).
			Count(&count).Error
		if err != nil {
			return false, "", err
		}
		if count > 0 {
			return false, "本周已完成该任务", nil
		}

	case integral.TaskTypeMonthly:
		// 每月任务：检查本月是否已完成
		monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		var count int64
		err = tx.Model(&integral.SysUserTask{}).
			Where("user_id = ? AND task_id = ? AND created_at >= ? AND status = ?",
				userID, task.ID, monthStart, integral.UserTaskStatusCompleted).
			Count(&count).Error
		if err != nil {
			return false, "", err
		}
		if count > 0 {
			return false, "本月已完成该任务", nil
		}

	case integral.TaskTypeDailyRepeatable:
		// 每日可重复任务：不做限制检查，可以无限次完成
		// 直接返回可以完成
		return true, "", nil

	case integral.TaskTypeOnce:
		// 一次性任务：检查是否已完成过
		if !task.IsRepeatable {
			var count int64
			err = tx.Model(&integral.SysUserTask{}).
				Where("user_id = ? AND task_id = ? AND status = ?",
					userID, task.ID, integral.UserTaskStatusCompleted).
				Count(&count).Error
			if err != nil {
				return false, "", err
			}
			if count > 0 {
				return false, "该任务已完成，不可重复", nil
			}
		}
	}

	return true, "", nil
}

// GetTaskByID 根据ID获取任务
func (taskService *TaskService) GetTaskByID(id uint) (task integral.SysTask, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&task).Error
	return
}

// AddPointsWithTaskRecord 添加积分并记录任务完成记录
// 用于系统自动加积分的场景（买会员、定时任务、免费积分等）
func (taskService *TaskService) AddPointsWithTaskRecord(userID uint, points int, reason string, pointsType string, taskID uint) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 1. 获取任务信息（如果taskID > 0）
		var task integral.SysTask
		if taskID > 0 {
			if err := tx.Where("id = ?", taskID).First(&task).Error; err != nil {
				return fmt.Errorf("获取任务信息失败: %v", err)
			}
		}

		// 2. 记录用户任务完成记录
		userTask := integral.SysUserTask{
			UserID: userID,
			TaskID: taskID,
			Reward: points,
			Status: integral.UserTaskStatusCompleted,
			Remark: reason,
		}
		if err := tx.Create(&userTask).Error; err != nil {
			return fmt.Errorf("记录任务完成失败: %v", err)
		}

		// 3. 更新用户积分
		if err := tx.Model(&system.SysUser{}).Where("id = ?", userID).
			UpdateColumn("points", gorm.Expr("points + ?", points)).Error; err != nil {
			return fmt.Errorf("更新用户积分失败: %v", err)
		}

		// 4. 记录积分流水
		pointsRecord := integral.SysUserPoints{
			UserID:     userID,
			Change:     points,
			Reason:     reason,
			Type:       pointsType,
			TaskID:     taskID,
			UserTaskID: userTask.ID,
		}
		if err := tx.Create(&pointsRecord).Error; err != nil {
			return fmt.Errorf("记录积分流水失败: %v", err)
		}

		return nil
	})
}

// AddFreePointsWithTaskRecord 添加免费积分并记录任务完成记录
func (taskService *TaskService) AddFreePointsWithTaskRecord(userID uint, points int, reason string, taskID uint) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 1. 获取任务信息（如果taskID > 0）
		var task integral.SysTask
		if taskID > 0 {
			if err := tx.Where("id = ?", taskID).First(&task).Error; err != nil {
				return fmt.Errorf("获取任务信息失败: %v", err)
			}
		}

		// 2. 记录用户任务完成记录
		userTask := integral.SysUserTask{
			UserID: userID,
			TaskID: taskID,
			Reward: points,
			Status: integral.UserTaskStatusCompleted,
			Remark: reason,
		}
		if err := tx.Create(&userTask).Error; err != nil {
			return fmt.Errorf("记录任务完成失败: %v", err)
		}

		// 3. 更新用户免费积分
		if err := tx.Model(&system.SysUser{}).Where("id = ?", userID).
			UpdateColumn("free_points", gorm.Expr("free_points + ?", points)).Error; err != nil {
			return fmt.Errorf("更新用户免费积分失败: %v", err)
		}

		// 4. 记录积分流水
		pointsRecord := integral.SysUserPoints{
			UserID:     userID,
			Change:     points,
			Reason:     reason,
			Type:       "free_points",
			TaskID:     taskID,
			UserTaskID: userTask.ID,
		}
		if err := tx.Create(&pointsRecord).Error; err != nil {
			return fmt.Errorf("记录积分流水失败: %v", err)
		}

		return nil
	})
}
