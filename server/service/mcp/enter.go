package mcp

type ServiceGroup struct {
	ProjectToolsService ProjectToolsService
	ProjectsService     ProjectsService
	McpClientService    McpClientService
	ApiKeyService       *ApiKeyService
	UseCaseService      UseCaseService
}

func NewServiceGroup() ServiceGroup {
	return ServiceGroup{
		ProjectToolsService: ProjectToolsService{},
		ProjectsService:     ProjectsService{},
		McpClientService:    McpClientService{},
		ApiKeyService:       ApiKeyServiceApp,
		UseCaseService:      UseCaseService{},
	}
}
