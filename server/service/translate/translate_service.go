package translate

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/basetypes"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/translate"
)

type TranslateService struct{}

func (s *TranslateService) BaiduTranslate(text string, from, to basetypes.Language) (*translate.BaiduTranslateResponse, error) {
	return translate.BaiduTranslate(text, string(from), string(to))
}

func (s *TranslateService) ListLanguages() []map[string]string {
	var langs []map[string]string
	for _, code := range basetypes.AllLanguageCodes {
		langs = append(langs, map[string]string{
			"code":  string(code),
			"cname": basetypes.LanguageNameMap[code],
			"ename": basetypes.LanguageEnameMap[code],
		})
	}
	return langs
}
